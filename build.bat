@echo off
echo === EMS Box 批量更新工具构建脚本 ===

REM 检查Go是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: Go未安装，请先安装Go语言环境
    pause
    exit /b 1
)

REM 检查Node.js是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 警告: Node.js未安装，将跳过前端构建
    set SKIP_FRONTEND=true
) else (
    set SKIP_FRONTEND=false
)

REM 创建必要的目录
echo 创建必要的目录...
if not exist uploads mkdir uploads
if not exist static\dist mkdir static\dist

REM 安装Go依赖
echo 安装Go依赖...
go mod tidy

REM 构建前端（如果Node.js可用）
if "%SKIP_FRONTEND%"=="false" (
    echo 构建前端...
    cd static
    if not exist node_modules (
        echo 安装前端依赖...
        call npm install
    )
    echo 构建前端资源...
    call npm run build
    cd ..
) else (
    echo 跳过前端构建
)

REM 构建Go程序
echo 构建Go程序...
go build -o emsbox-updater.exe main.go

if %errorlevel% equ 0 (
    echo ✅ 构建成功！
    echo.
    echo 使用方法:
    echo 1. 编辑 config.txt 文件，配置SSH连接信息
    echo 2. 运行程序: emsbox-updater.exe
    echo 3. 打开浏览器访问: http://localhost:8080
) else (
    echo ❌ 构建失败！
    pause
    exit /b 1
)

pause 