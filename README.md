# EMS Box 批量更新工具

这是一个用于批量更新多台Linux开发板程序的工具，支持通过SSH连接和SCP命令传输文件。

## 功能特性

- 📝 支持SSH配置文件管理（IP、用户名、密码格式）
- 🚀 批量文件传输到多台设备
- 🎯 实时显示传输状态和进度
- 📦 自动解压tar压缩包
- 🔄 智能备份功能（可选备份旧gateway文件夹）
- 🧹 自动清理临时文件
- 🌐 现代化Web界面
- 🔒 安全的SSH连接和文件传输

## 项目结构

```
EMSBOXUPDATER/
├── main.go              # Go后端主程序
├── go.mod              # Go模块依赖
├── config.txt          # SSH配置文件示例
├── uploads/            # 上传文件存储目录
├── static/
│   └── simple.html     # Web界面
└── README.md           # 项目说明
```

## 快速开始

### 1. 配置SSH连接信息

编辑 `config.txt` 文件，每行一个SSH配置，格式为：`IP 用户名 密码`

```
************* root password123
************* admin admin123
************* user userpass
```

### 2. 启动服务

```bash
# 安装Go依赖
go mod tidy

# 构建程序
go build -o emsboxupdater.exe

# 启动服务
./emsboxupdater.exe
```

服务将在 `http://localhost:9999` 启动

### 3. 使用Web界面

1. 打开浏览器访问 `http://localhost:9999`
2. 自动加载SSH配置文件
3. 上传要传输的tar压缩包文件
4. 设置目标目录路径（默认：/user_part）
5. 选择是否备份旧的gateway文件夹
6. 点击"开始批量更新"执行传输

## 工作流程

### 完整处理流程：
1. **连接SSH** - 建立SSH连接
2. **传输文件** - 通过SCP传输压缩包
3. **处理文件** - 检查目标目录状态
4. **备份文件** - 如果选择备份且存在gateway文件夹，执行 `cp -R gateway gateway_YYYYMMDD`
5. **解压文件** - 执行 `tar -xf` 解压（覆盖模式）
6. **清理文件** - 删除传输的压缩包
7. **完成** - 显示最终结果

### 状态显示：
- 🔵 连接中 → 🟡 传输中 → 🟣 处理中 → 🔴 备份中 → 🟢 解压中 → 🔵 清理中 → ✅ 成功

## 主要API接口

### GET /api/ssh-configs
获取SSH配置列表

### POST /api/upload
上传文件

### POST /api/update-progress
执行批量更新（带实时进度）

## 技术栈

### 后端
- **Go 1.21+**: 主要编程语言
- **Gin**: Web框架
- **golang.org/x/crypto/ssh**: SSH客户端库

### 前端
- **纯HTML/CSS/JavaScript**: 轻量级Web界面
- **Server-Sent Events (SSE)**: 实时进度更新

## 安全说明

⚠️ **注意：此工具仅用于开发和测试环境。在生产环境中使用时，请注意以下安全事项：**

1. SSH密码以明文形式存储在配置文件中，请确保文件权限安全
2. 建议使用SSH密钥认证替代密码认证
3. 限制工具的网络访问权限
4. 定期更新SSH密码

## 许可证

MIT License 