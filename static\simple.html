<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMS Box 批量更新工具</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .header {
            background-color: #409EFF;
            color: white;
            text-align: center;
            padding: 20px 0;
        }
        .main-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .card-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #303133;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #606266;
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn-primary {
            background-color: #409EFF;
            color: white;
        }
        .btn-success {
            background-color: #67C23A;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .upload-area {
            border: 2px dashed #DCDFE6;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .upload-area:hover {
            border-color: #409EFF;
        }
        .upload-area.dragover {
            border-color: #409EFF;
            background-color: #f0f9ff;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .table th, .table td {
            padding: 8px 12px;
            border: 1px solid #EBEEF5;
            text-align: left;
        }
        .table th {
            background-color: #F5F7FA;
            font-weight: 500;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .tag-success {
            background-color: #f0f9ff;
            color: #67C23A;
            border: 1px solid #b3e19d;
        }
        .tag-danger {
            background-color: #fef0f0;
            color: #F56C6C;
            border: 1px solid #fbc4c4;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #409EFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
        .flex {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .flex-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>EMS Box 批量更新工具</h1>
    </div>

    <div class="main-container">
        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
            <!-- 左侧配置区域 -->
            <div class="card">
                <div class="card-header">SSH配置管理</div>
                
                <div class="form-group">
                    <label class="form-label">配置文件:</label>
                    <input type="text" class="form-input" id="configFile" value="config.txt">
                </div>
                
                <div class="form-group">
                    <button class="btn btn-primary" onclick="loadSSHConfigs()" id="loadBtn">
                        <span id="loadSpinner" class="loading hidden"></span>
                        加载配置
                    </button>
                </div>
                
                <div id="sshConfigsSection" class="hidden">
                    <div class="flex-between" style="margin-bottom: 10px;">
                        <h4 id="configCount">SSH配置列表</h4>
                        <div class="flex">
                            <button class="btn" onclick="selectAllDevices()" style="font-size: 12px; padding: 4px 8px;">全选</button>
                            <button class="btn" onclick="selectNoneDevices()" style="font-size: 12px; padding: 4px 8px;">全不选</button>
                        </div>
                    </div>
                    <table class="table" id="sshConfigsTable">
                        <thead>
                            <tr>
                                <th style="width: 40px;">选择</th>
                                <th>IP地址</th>
                                <th>用户名</th>
                                <th>密码</th>
                            </tr>
                        </thead>
                        <tbody id="sshConfigsBody">
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 右侧操作区域 -->
            <div class="card">
                <div class="card-header">文件更新操作</div>
                
                <div class="form-group">
                    <label class="form-label">选择文件:</label>
                    <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                        <input type="file" id="fileInput" accept=".tar,.tar.gz,.zip" style="display: none;" onchange="handleFileSelect(event)">
                        <div>
                            <p style="margin: 0 0 5px 0;">📁 将文件拖到此处，或点击选择文件</p>
                            <p style="color: #909399; font-size: 12px; margin: 0;">支持 .tar, .tar.gz, .zip 格式文件</p>
                        </div>
                    </div>
                </div>
                
                <div class="form-group hidden" id="filePathGroup">
                    <label class="form-label">文件路径:</label>
                    <input type="text" class="form-input" id="filePath" readonly>
                </div>
                
                                 <div class="form-group">
                     <label class="form-label">目标目录:</label>
                     <input type="text" class="form-input" id="targetDir" value="/user_part">
                 </div>
                 
                 <div class="form-group">
                     <label class="form-label">
                         <input type="checkbox" id="backupGateway" checked style="margin-right: 8px;">
                         备份旧的gateway文件夹
                     </label>
                     <div style="font-size: 12px; color: #666; margin-top: 4px;">
                         如果目标目录存在gateway文件夹，选中此项将复制备份为gateway_YYYYMMDD后再解压覆盖
                     </div>
                 </div>

                 <div class="form-group">
                     <label class="form-label">操作模式:</label>
                     <select class="form-input" id="operationMode" onchange="updateOperationDisplay()">
                         <option value="">请选择操作模式</option>
                         <!-- 操作模式选项将通过JavaScript动态加载 -->
                         <option value="custom">自定义模式</option>
                     </select>
                 </div>

                 <div class="form-group hidden" id="operationDisplay">
                     <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 12px;">
                         <div style="margin-bottom: 8px;">
                             <strong style="color: #28a745;">更新前执行命令:</strong>
                             <div id="preCommands" style="font-family: monospace; font-size: 12px; color: #666; margin-top: 4px; white-space: pre-line;"></div>
                         </div>
                         <div>
                             <strong style="color: #dc3545;">更新后执行命令:</strong>
                             <div id="postCommands" style="font-family: monospace; font-size: 12px; color: #666; margin-top: 4px; white-space: pre-line;"></div>
                         </div>
                     </div>
                 </div>

                 <div class="form-group hidden" id="customCommandsGroup">
                     <label class="form-label">自定义更新前命令 (每行一个命令):</label>
                     <textarea class="form-input" id="customPreCommands" rows="3" placeholder="例如:&#10;systemctl stop myservice&#10;killall myprocess"></textarea>

                     <label class="form-label" style="margin-top: 10px;">自定义更新后命令 (每行一个命令):</label>
                     <textarea class="form-input" id="customPostCommands" rows="3" placeholder="例如:&#10;systemctl start myservice&#10;systemctl enable myservice"></textarea>
                 </div>
                
                <div class="form-group">
                    <button class="btn btn-success" onclick="startUpdate()" id="updateBtn" disabled>
                        <span id="updateSpinner" class="loading hidden"></span>
                        开始批量更新
                    </button>
                    <button class="btn" onclick="resetForm()">重置</button>
                </div>
            </div>
        </div>
        
                 <!-- 实时进度 -->
         <div class="card hidden" id="progressSection">
             <div class="card-header">
                 <span>传输进度</span>
             </div>
             
             <div style="margin-bottom: 15px;">
                 <div style="background: #f5f5f5; border-radius: 4px; height: 20px; overflow: hidden;">
                     <div id="progressBar" style="background: #409EFF; height: 100%; width: 0%; transition: width 0.3s;"></div>
                 </div>
                 <div id="progressText" style="text-align: center; margin-top: 5px; font-size: 14px; color: #606266;">准备开始...</div>
             </div>
             
             <table class="table" id="progressTable">
                 <thead>
                     <tr>
                         <th>设备IP</th>
                         <th>状态</th>
                         <th>详细信息</th>
                     </tr>
                 </thead>
                 <tbody id="progressBody">
                 </tbody>
             </table>
         </div>

         <!-- 更新结果 -->
         <div class="card hidden" id="resultsSection">
             <div class="card-header flex-between">
                 <span>更新结果</span>
                 <span class="tag" id="resultsSummary"></span>
             </div>
             
             <table class="table" id="resultsTable">
                 <thead>
                     <tr>
                         <th>设备IP</th>
                         <th>状态</th>
                         <th>详细信息</th>
                     </tr>
                 </thead>
                 <tbody id="resultsBody">
                 </tbody>
             </table>
         </div>
    </div>

    <script>
        let sshConfigs = [];
        let selectedFile = null;
        let operationModes = {}; // 从服务器加载的操作模式

        // 拖拽上传功能
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 加载操作模式
        async function loadOperationModes() {
            try {
                const response = await fetch('/api/operation-modes?configFile=operation-modes.ini');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载操作模式失败');
                }

                operationModes = data.modes || {};
                updateOperationModeOptions();
                console.log('成功加载操作模式:', operationModes);

            } catch (error) {
                console.error('加载操作模式失败:', error);
                showMessage('加载操作模式失败: ' + error.message, 'warning');
                operationModes = {};
            }
        }

        // 更新操作模式选项
        function updateOperationModeOptions() {
            const modeSelect = document.getElementById('operationMode');

            // 清空现有选项（保留默认选项和自定义选项）
            while (modeSelect.children.length > 1) {
                if (modeSelect.children[modeSelect.children.length - 1].value !== 'custom') {
                    modeSelect.removeChild(modeSelect.children[modeSelect.children.length - 1]);
                } else {
                    break;
                }
            }

            // 添加从配置文件加载的模式
            Object.keys(operationModes).forEach(modeKey => {
                const mode = operationModes[modeKey];
                const option = document.createElement('option');
                option.value = modeKey;
                option.textContent = mode.name + (mode.description ? ' - ' + mode.description : '');

                // 在自定义选项之前插入
                const customOption = modeSelect.querySelector('option[value="custom"]');
                modeSelect.insertBefore(option, customOption);
            });
        }

        // 加载SSH配置
        async function loadSSHConfigs() {
            const configFile = document.getElementById('configFile').value;
            const loadBtn = document.getElementById('loadBtn');
            const loadSpinner = document.getElementById('loadSpinner');
            
            loadBtn.disabled = true;
            loadSpinner.classList.remove('hidden');
            
            try {
                const response = await fetch(`/api/ssh-configs?configFile=${encodeURIComponent(configFile)}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || '加载配置失败');
                }
                
                sshConfigs = data.configs;
                displaySSHConfigs();
                updateUpdateButton();
                showMessage(`成功加载 ${sshConfigs.length} 个SSH配置`, 'success');
                
            } catch (error) {
                showMessage('加载SSH配置失败: ' + error.message, 'error');
                sshConfigs = [];
                document.getElementById('sshConfigsSection').classList.add('hidden');
            } finally {
                loadBtn.disabled = false;
                loadSpinner.classList.add('hidden');
            }
        }

        // 显示SSH配置
        function displaySSHConfigs() {
            const section = document.getElementById('sshConfigsSection');
            const count = document.getElementById('configCount');
            const tbody = document.getElementById('sshConfigsBody');

            count.textContent = `SSH配置列表 (${sshConfigs.length}台设备)`;

            tbody.innerHTML = '';
            sshConfigs.forEach((config, index) => {
                const row = tbody.insertRow();

                // 添加复选框列
                const checkboxCell = row.insertCell(0);
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = true; // 默认选中
                checkbox.id = `device-${index}`;
                checkbox.onchange = updateUpdateButton;
                checkboxCell.appendChild(checkbox);

                row.insertCell(1).textContent = config.ip;
                row.insertCell(2).textContent = config.username;
                row.insertCell(3).textContent = config.password;
            });

            section.classList.remove('hidden');
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            selectedFile = file;
            document.getElementById('filePath').value = file.name;
            document.getElementById('filePathGroup').classList.remove('hidden');
            updateUpdateButton();
        }

        // 更新操作模式显示
        function updateOperationDisplay() {
            const modeSelect = document.getElementById('operationMode');
            const operationDisplay = document.getElementById('operationDisplay');
            const customCommandsGroup = document.getElementById('customCommandsGroup');
            const preCommands = document.getElementById('preCommands');
            const postCommands = document.getElementById('postCommands');

            const selectedMode = modeSelect.value;

            if (selectedMode === 'custom') {
                operationDisplay.classList.add('hidden');
                customCommandsGroup.classList.remove('hidden');
            } else if (selectedMode && operationModes[selectedMode]) {
                const mode = operationModes[selectedMode];
                preCommands.textContent = (mode.preCommands || []).join('\n');
                postCommands.textContent = (mode.postCommands || []).join('\n');
                operationDisplay.classList.remove('hidden');
                customCommandsGroup.classList.add('hidden');
            } else {
                operationDisplay.classList.add('hidden');
                customCommandsGroup.classList.add('hidden');
            }

            updateUpdateButton();
        }

        // 获取当前选择的操作命令
        function getOperationCommands() {
            const modeSelect = document.getElementById('operationMode');
            const selectedMode = modeSelect.value;

            if (selectedMode === 'custom') {
                const preCommands = document.getElementById('customPreCommands').value
                    .split('\n')
                    .map(cmd => cmd.trim())
                    .filter(cmd => cmd.length > 0);
                const postCommands = document.getElementById('customPostCommands').value
                    .split('\n')
                    .map(cmd => cmd.trim())
                    .filter(cmd => cmd.length > 0);
                return { preCommands, postCommands };
            } else if (selectedMode && operationModes[selectedMode]) {
                return {
                    preCommands: operationModes[selectedMode].preCommands || [],
                    postCommands: operationModes[selectedMode].postCommands || []
                };
            }

            return { preCommands: [], postCommands: [] };
        }

        // 更新按钮状态
        function updateUpdateButton() {
            const updateBtn = document.getElementById('updateBtn');
            const selectedDevices = getSelectedDevices();
            const operationMode = document.getElementById('operationMode').value;
            const canUpdate = selectedDevices.length > 0 &&
                            selectedFile &&
                            document.getElementById('targetDir').value.trim() &&
                            operationMode;
            updateBtn.disabled = !canUpdate;

            // 更新按钮文本显示选中的设备数量
            if (selectedDevices.length > 0) {
                updateBtn.innerHTML = `<span id="updateSpinner" class="loading hidden"></span>开始批量更新 (${selectedDevices.length}台设备)`;
            } else {
                updateBtn.innerHTML = `<span id="updateSpinner" class="loading hidden"></span>开始批量更新`;
            }
        }

        // 获取选中的设备
        function getSelectedDevices() {
            const selectedDevices = [];
            sshConfigs.forEach((config, index) => {
                const checkbox = document.getElementById(`device-${index}`);
                if (checkbox && checkbox.checked) {
                    selectedDevices.push(config);
                }
            });
            return selectedDevices;
        }

        // 全选设备
        function selectAllDevices() {
            sshConfigs.forEach((config, index) => {
                const checkbox = document.getElementById(`device-${index}`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            updateUpdateButton();
        }

        // 全不选设备
        function selectNoneDevices() {
            sshConfigs.forEach((config, index) => {
                const checkbox = document.getElementById(`device-${index}`);
                if (checkbox) {
                    checkbox.checked = false;
                }
            });
            updateUpdateButton();
        }

        // 开始更新（带实时进度）
        async function startUpdate() {
            if (!selectedFile) {
                showMessage('请先选择要上传的文件', 'error');
                return;
            }

            const selectedDevices = getSelectedDevices();
            if (selectedDevices.length === 0) {
                showMessage('请至少选择一台设备', 'error');
                return;
            }

            // 获取操作命令
            const operationCommands = getOperationCommands();
            let confirmMessage = `确定要向 ${selectedDevices.length} 台设备传输文件吗？\n\n`;

            if (operationCommands.preCommands.length > 0) {
                confirmMessage += `更新前将执行命令:\n${operationCommands.preCommands.join('\n')}\n\n`;
            }

            if (operationCommands.postCommands.length > 0) {
                confirmMessage += `更新后将执行命令:\n${operationCommands.postCommands.join('\n')}`;
            }

            if (!confirm(confirmMessage)) {
                return;
            }

            // 保存操作命令到全局变量，供后续使用
            window.currentOperationCommands = operationCommands;

            console.log('开始更新，选中的SSH配置:', selectedDevices);
            
            const updateBtn = document.getElementById('updateBtn');
            const updateSpinner = document.getElementById('updateSpinner');
            
            updateBtn.disabled = true;
            updateSpinner.classList.remove('hidden');
            
            // 显示进度区域
            const progressSection = document.getElementById('progressSection');
            const resultsSection = document.getElementById('resultsSection');
            progressSection.classList.remove('hidden');
            resultsSection.classList.add('hidden');
            
            // 初始化进度显示
            const progressBody = document.getElementById('progressBody');
            progressBody.innerHTML = '';
            selectedDevices.forEach(config => {
                const row = progressBody.insertRow();
                row.id = 'progress-' + config.ip;
                row.insertCell(0).textContent = config.ip;
                row.insertCell(1).innerHTML = '<span class="tag">等待中</span>';
                row.insertCell(2).textContent = '等待开始...';
            });
            
            try {
                // 上传文件
                showMessage('正在上传文件...', 'info');
                const formData = new FormData();
                formData.append('file', selectedFile);
                
                const uploadResponse = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const uploadData = await uploadResponse.json();
                if (!uploadResponse.ok) {
                    throw new Error(uploadData.error || '文件上传失败');
                }
                
                // 开始实时进度更新
                showMessage('开始批量传输文件...', 'info');
                startProgressUpdates(uploadData.filename, selectedDevices);
                
            } catch (error) {
                showMessage('更新失败: ' + error.message, 'error');
                updateBtn.disabled = false;
                updateSpinner.classList.add('hidden');
                updateUpdateButton();
            }
        }

        // 启动实时进度更新
        function startProgressUpdates(sourceFile, selectedDevices) {
            console.log('开始启动实时进度更新，sourceFile:', sourceFile);
            console.log('选中的SSH配置:', selectedDevices);
            
            // 发送更新请求并处理SSE
            fetch('/api/update-progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sourceFile: sourceFile,
                    targetDir: document.getElementById('targetDir').value,
                    sshConfigs: selectedDevices,
                    backupGateway: document.getElementById('backupGateway').checked,
                    preCommands: window.currentOperationCommands.preCommands,
                    postCommands: window.currentOperationCommands.postCommands
                })
            }).then(response => {
                console.log('收到响应:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error('Failed to start progress updates');
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('SSE流结束');
                            finishUpdate();
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        console.log('收到SSE数据块:', chunk);
                        buffer += chunk;
                        const lines = buffer.split('\n');
                        
                        // 保留最后一个不完整的行
                        buffer = lines.pop() || '';
                        
                        let currentEvent = null;
                        
                        for (const line of lines) {
                            console.log('处理SSE行:', line);
                            
                            if (line.startsWith('event:')) {
                                currentEvent = line.substring(6).trim();
                                console.log('发现事件行:', currentEvent);
                                continue;
                            }
                            
                            if (line.startsWith('data:')) {
                                try {
                                    const jsonData = line.substring(5).trim();
                                    console.log('解析JSON数据:', jsonData);
                                    console.log('JSON数据长度:', jsonData.length);
                                    console.log('当前事件类型:', currentEvent);
                                    
                                    if (jsonData && currentEvent === 'progress') {
                                        console.log('开始解析JSON...');
                                        const data = JSON.parse(jsonData);
                                        console.log('解析成功，调用updateProgress:', data);
                                        updateProgress(data);
                                        
                                        if (data.status === 'completed') {
                                            finishUpdate();
                                            return;
                                        }
                                    } else if (!jsonData) {
                                        console.log('JSON数据为空');
                                    } else {
                                        console.log('非progress事件，忽略');
                                    }
                                } catch (e) {
                                    console.error('Error parsing SSE data:', e, 'Line:', line);
                                    console.error('JSON数据:', line.substring(6).trim());
                                }
                            }
                            
                            // 重置事件类型（空行表示事件结束）
                            if (line.trim() === '') {
                                currentEvent = null;
                            }
                        }
                        
                        return readStream();
                    });
                }
                
                return readStream();
            }).catch(error => {
                console.error('Progress update error:', error);
                showMessage('连接中断，请重试', 'error');
                finishUpdate();
            });
        }

        // 更新进度显示
        function updateProgress(progress) {
            console.log('收到进度更新:', progress);
            
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            // 更新总进度条
            progressBar.style.width = progress.progress + '%';
            progressText.textContent = `进度: ${progress.progress}%`;
            
            if (progress.ip === 'summary') {
                progressText.textContent = progress.message;
                return;
            }
            
            // 更新设备状态
            const row = document.getElementById('progress-' + progress.ip);
            if (row) {
                const statusCell = row.cells[1];
                const messageCell = row.cells[2];
                
                                                let statusTag = '';
                                switch (progress.status) {
                                    case 'connecting':
                                        statusTag = '<span class="tag" style="background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff;">连接中</span>';
                                        break;
                                    case 'connected':
                                        statusTag = '<span class="tag" style="background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f;">已连接</span>';
                                        break;
                                    case 'pre-command':
                                        statusTag = '<span class="tag" style="background: #fff0f6; color: #c41d7f; border: 1px solid #ffadd2;">执行前置命令</span>';
                                        break;
                                    case 'transferring':
                                        statusTag = '<span class="tag" style="background: #fff7e6; color: #fa8c16; border: 1px solid #ffd591;">传输中</span>';
                                        break;
                                    case 'processing':
                                        statusTag = '<span class="tag" style="background: #f0f5ff; color: #597ef7; border: 1px solid #adc6ff;">处理中</span>';
                                        break;
                                    case 'backing':
                                        statusTag = '<span class="tag" style="background: #fff1f0; color: #ff4d4f; border: 1px solid #ffccc7;">备份中</span>';
                                        break;
                                    case 'extracting':
                                        statusTag = '<span class="tag" style="background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f;">解压中</span>';
                                        break;
                                    case 'cleaning':
                                        statusTag = '<span class="tag" style="background: #f0f5ff; color: #597ef7; border: 1px solid #adc6ff;">清理中</span>';
                                        break;
                                    case 'post-command':
                                        statusTag = '<span class="tag" style="background: #f0f5ff; color: #722ed1; border: 1px solid #d3adf7;">执行后置命令</span>';
                                        break;
                                    case 'success':
                                        statusTag = '<span class="tag tag-success">成功</span>';
                                        break;
                                    case 'error':
                                        statusTag = '<span class="tag tag-danger">失败</span>';
                                        break;
                                }
                
                statusCell.innerHTML = statusTag;
                messageCell.textContent = progress.message;
                console.log('更新设备状态:', progress.ip, progress.status, progress.message);
            } else {
                console.error('找不到设备行:', 'progress-' + progress.ip);
            }
        }

        // 完成更新
        function finishUpdate() {
            const updateBtn = document.getElementById('updateBtn');
            const updateSpinner = document.getElementById('updateSpinner');
            
            updateBtn.disabled = false;
            updateSpinner.classList.add('hidden');
            updateUpdateButton();
            
            showMessage('传输完成！', 'success');
        }

        // 显示结果
        function displayResults(data) {
            const section = document.getElementById('resultsSection');
            const summary = document.getElementById('resultsSummary');
            const tbody = document.getElementById('resultsBody');
            
            summary.textContent = data.summary;
            const successCount = data.results.filter(r => r.success).length;
            const totalCount = data.results.length;
            
            if (successCount === totalCount) {
                summary.className = 'tag tag-success';
            } else if (successCount === 0) {
                summary.className = 'tag tag-danger';
            } else {
                summary.className = 'tag';
            }
            
            tbody.innerHTML = '';
            data.results.forEach(result => {
                const row = tbody.insertRow();
                row.insertCell(0).textContent = result.ip;
                
                const statusCell = row.insertCell(1);
                const statusTag = document.createElement('span');
                statusTag.className = result.success ? 'tag tag-success' : 'tag tag-danger';
                statusTag.textContent = result.success ? '成功' : '失败';
                statusCell.appendChild(statusTag);
                
                row.insertCell(2).textContent = result.message;
            });
            
            section.classList.remove('hidden');
        }

                 // 重置表单
         function resetForm() {
             selectedFile = null;
             document.getElementById('filePath').value = '';
             document.getElementById('filePathGroup').classList.add('hidden');
             document.getElementById('targetDir').value = '/user_part';
             document.getElementById('operationMode').value = '';
             document.getElementById('operationDisplay').classList.add('hidden');
             document.getElementById('customCommandsGroup').classList.add('hidden');
             document.getElementById('customPreCommands').value = '';
             document.getElementById('customPostCommands').value = '';
             document.getElementById('resultsSection').classList.add('hidden');
             document.getElementById('progressSection').classList.add('hidden');
             document.getElementById('fileInput').value = '';
             updateUpdateButton();
         }

        // 显示消息
        function showMessage(message, type) {
            // 简单的消息显示，可以替换为更好的UI组件
            const colors = {
                success: '#67C23A',
                error: '#F56C6C',
                warning: '#E6A23C',
                info: '#409EFF'
            };
            
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 12px 20px;
                border-radius: 4px;
                z-index: 1000;
                max-width: 300px;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            `;
            messageDiv.textContent = message;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 3000);
        }

        // 页面加载时自动加载配置
        window.addEventListener('load', async () => {
            await loadOperationModes(); // 先加载操作模式
            await loadSSHConfigs();     // 再加载SSH配置
        });

        // 监听目标目录输入变化
        document.getElementById('targetDir').addEventListener('input', updateUpdateButton);

        // 监听自定义命令输入变化
        document.getElementById('customPreCommands').addEventListener('input', updateUpdateButton);
        document.getElementById('customPostCommands').addEventListener('input', updateUpdateButton);
    </script>
</body>
</html> 