# 操作模式配置说明

## 概述

操作模式配置文件 `operation-modes.ini` 允许用户自定义更新前后要执行的命令。系统会在页面加载时自动读取此配置文件，并在界面上显示可选的操作模式。

## 配置文件格式

配置文件使用 INI 格式，每个操作模式为一个独立的节（section）。

### 基本结构

```ini
[模式ID]
name = 模式显示名称
description = 模式描述（可选）
pre_commands = 更新前命令1;更新前命令2;更新前命令3
post_commands = 更新后命令1;更新后命令2;更新后命令3
```

### 字段说明

- **模式ID**: 唯一标识符，建议使用 `mode1`, `mode2` 等格式
- **name**: 在界面下拉菜单中显示的名称
- **description**: 模式的详细描述，会显示在下拉菜单中（可选）
- **pre_commands**: 更新前执行的命令，多个命令用分号(`;`)分隔
- **post_commands**: 更新后执行的命令，多个命令用分号(`;`)分隔

## 配置示例

### 标准重启模式
```ini
[mode1]
name = 标准重启模式
description = 停止服务、清理临时文件，然后重启服务
pre_commands = systemctl stop gateway;killall -9 gateway;rm -rf /tmp/gateway_backup
post_commands = chmod +x /user_part/gateway/gateway;systemctl start gateway
```

### 生产环境模式
```ini
[mode5]
name = 生产环境模式
description = 生产环境安全部署，包含完整的备份和验证
pre_commands = systemctl stop gateway;systemctl stop nginx;cp -r /user_part/gateway /backup/gateway_$(date +%Y%m%d_%H%M%S);sync
post_commands = chmod +x /user_part/gateway/gateway;systemctl start gateway;sleep 5;systemctl start nginx;curl -f http://localhost:8080/health || systemctl stop gateway
```

## 执行流程

1. **更新前命令执行**: 按配置文件中的顺序依次执行 `pre_commands` 中的命令
2. **文件传输和处理**: 上传文件、备份、解压等操作
3. **更新后命令执行**: 按配置文件中的顺序依次执行 `post_commands` 中的命令

## 注意事项

### 命令分隔
- 多个命令必须用分号(`;`)分隔
- 命令中不能包含分号，如需要请使用其他方式

### 命令执行环境
- 所有命令都在远程Linux设备上通过SSH执行
- 命令执行环境为登录用户的默认shell
- 建议使用绝对路径避免路径问题

### 错误处理
- 如果任何一个更新前命令执行失败，整个更新过程会停止
- 如果更新后命令执行失败，会标记为失败状态但不影响其他设备

### 安全建议
- 避免在命令中使用硬编码的敏感信息
- 生产环境建议添加命令执行验证
- 建议在测试环境充分验证命令后再用于生产

## 动态重载

修改 `operation-modes.ini` 文件后，需要重启服务器才能生效。未来版本可能会支持动态重载配置。

## 自定义模式

除了配置文件中的预定义模式，用户还可以在界面上选择"自定义模式"，手动输入更新前后的命令。

## 故障排除

### 配置文件不存在
如果 `operation-modes.ini` 文件不存在，系统会正常启动，但操作模式下拉菜单中只会显示"自定义模式"选项。

### 配置文件格式错误
如果配置文件格式有误，系统会在日志中显示错误信息，并跳过有问题的模式配置。

### 命令执行失败
命令执行失败的详细信息会在界面的进度显示中展示，便于用户排查问题。
