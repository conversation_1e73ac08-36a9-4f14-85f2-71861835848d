package main

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/go-ini/ini"
	"golang.org/x/crypto/ssh"
)

// 全局配置变量
var globalConfig *Config

// Config 应用配置
type Config struct {
	Server struct {
		Port int `ini:"port"`
	} `ini:"server"`
	SSH struct {
		Timeout int `ini:"timeout"`
	} `ini:"ssh"`
}

// SSHConfig SSH连接配置
type SSHConfig struct {
	IP       string `json:"ip"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// UpdateRequest 更新请求结构
type UpdateRequest struct {
	ConfigFile    string `json:"configFile"`
	SourceFile    string `json:"sourceFile"`
	TargetDir     string `json:"targetDir"`
	SSHConfigs    []SSHConfig `json:"sshConfigs,omitempty"`
	BackupGateway bool   `json:"backupGateway"`
}

// UpdateResult 更新结果
type UpdateResult struct {
	IP      string `json:"ip"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// UpdateResponse 更新响应
type UpdateResponse struct {
	Results []UpdateResult `json:"results"`
	Summary string         `json:"summary"`
}

// 读取配置文件
func loadConfig(configPath string) (*Config, error) {
	config := &Config{}
	
	// 设置默认值
	config.Server.Port = 9999
	config.SSH.Timeout = 30
	
	// 如果配置文件不存在，创建默认配置文件
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		log.Printf("配置文件 %s 不存在，创建默认配置文件", configPath)
		
		// 创建默认配置文件
		defaultConfig := `[server]
# 服务器监听端口
port = 9999

[ssh]
# SSH连接超时时间（秒）
timeout = 30`
		
		if err := os.WriteFile(configPath, []byte(defaultConfig), 0644); err != nil {
			log.Printf("创建默认配置文件失败: %v", err)
		}
		
		return config, nil
	}
	
	// 读取配置文件
	cfg, err := ini.Load(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}
	
	// 解析配置
	if err := cfg.MapTo(config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}
	
	// 验证配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		log.Printf("警告: 端口号 %d 无效，使用默认端口 9999", config.Server.Port)
		config.Server.Port = 9999
	}
	
	if config.SSH.Timeout <= 0 {
		log.Printf("警告: SSH超时时间 %d 无效，使用默认值 30秒", config.SSH.Timeout)
		config.SSH.Timeout = 30
	}
	
	return config, nil
}

// 解析SSH配置文件
func parseSSHConfig(configFile string) ([]SSHConfig, error) {
	file, err := os.Open(configFile)
	if err != nil {
		return nil, fmt.Errorf("无法打开配置文件: %v", err)
	}
	defer file.Close()

	var configs []SSHConfig
	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		
		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) != 3 {
			log.Printf("警告: 第%d行格式不正确，跳过: %s", lineNum, line)
			continue
		}

		config := SSHConfig{
			IP:       parts[0],
			Username: parts[1],
			Password: parts[2],
		}
		configs = append(configs, config)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取配置文件时出错: %v", err)
	}

	return configs, nil
}

// 创建SSH客户端
func createSSHClient(config SSHConfig, timeout int) (*ssh.Client, error) {
	sshConfig := &ssh.ClientConfig{
		User: config.Username,
		Auth: []ssh.AuthMethod{
			ssh.Password(config.Password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         time.Duration(timeout) * time.Second,
	}

	client, err := ssh.Dial("tcp", config.IP+":22", sshConfig)
	if err != nil {
		return nil, fmt.Errorf("SSH连接失败: %v", err)
	}

	return client, nil
}

// 通过SSH执行SCP命令传输文件
func scpFile(client *ssh.Client, sourceFile, targetDir string) error {
	// 获取文件信息
	fileInfo, err := os.Stat(sourceFile)
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}

	filename := filepath.Base(sourceFile)
	
	// 确保目标目录存在
	createDirSession, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("创建目录会话失败: %v", err)
	}
	createDirCmd := fmt.Sprintf("mkdir -p %s", targetDir)
	createDirSession.Run(createDirCmd)
	createDirSession.Close()

	// 创建SSH会话
	session, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 打开源文件
	file, err := os.Open(sourceFile)
	if err != nil {
		return fmt.Errorf("无法打开源文件: %v", err)
	}
	defer file.Close()

	// 准备SCP命令
	scpCmd := fmt.Sprintf("scp -t %s", targetDir)
	
	// 获取会话的stdin和stdout
	stdin, err := session.StdinPipe()
	if err != nil {
		return fmt.Errorf("获取stdin失败: %v", err)
	}
	defer stdin.Close()

	stdout, err := session.StdoutPipe()
	if err != nil {
		return fmt.Errorf("获取stdout失败: %v", err)
	}

	// 启动SCP命令
	if err := session.Start(scpCmd); err != nil {
		return fmt.Errorf("启动SCP命令失败: %v", err)
	}

	// 等待SCP准备就绪
	response := make([]byte, 1)
	if _, err := stdout.Read(response); err != nil {
		return fmt.Errorf("读取SCP响应失败: %v", err)
	}

	if response[0] != 0 {
		return fmt.Errorf("SCP初始化失败")
	}

	// 发送文件头信息
	header := fmt.Sprintf("C0644 %d %s\n", fileInfo.Size(), filename)
	if _, err := stdin.Write([]byte(header)); err != nil {
		return fmt.Errorf("发送文件头失败: %v", err)
	}

	// 等待确认
	if _, err := stdout.Read(response); err != nil {
		return fmt.Errorf("读取文件头确认失败: %v", err)
	}

	if response[0] != 0 {
		return fmt.Errorf("文件头确认失败")
	}

	// 传输文件内容
	if _, err := io.Copy(stdin, file); err != nil {
		return fmt.Errorf("传输文件内容失败: %v", err)
	}

	// 发送结束标志
	if _, err := stdin.Write([]byte{0}); err != nil {
		return fmt.Errorf("发送结束标志失败: %v", err)
	}

	// 等待最终确认
	if _, err := stdout.Read(response); err != nil {
		return fmt.Errorf("读取最终确认失败: %v", err)
	}

	if response[0] != 0 {
		return fmt.Errorf("文件传输最终确认失败")
	}

	// 关闭stdin来告诉远程SCP命令传输完成
	stdin.Close()

	// 简化的等待逻辑 - 不等待session.Wait()，直接返回成功
	// 因为文件已经传输完成并得到确认
	return nil
}

// 通过SSH执行命令
func executeSSHCommand(client *ssh.Client, command string) error {
	// 创建SSH会话
	session, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("创建SSH会话失败: %v", err)
	}
	defer session.Close()

	// 执行命令
	log.Printf("执行SSH命令: %s", command)
	output, err := session.CombinedOutput(command)
	if err != nil {
		return fmt.Errorf("执行命令失败: %v, 输出: %s", err, string(output))
	}

	log.Printf("命令执行成功，输出: %s", string(output))
	return nil
}

// 进度更新结构
type ProgressUpdate struct {
	IP       string `json:"ip"`
	Status   string `json:"status"` // "connecting", "transferring", "success", "error"
	Message  string `json:"message"`
	Progress int    `json:"progress"` // 0-100
}

// 批量更新文件（带进度回调）
func batchUpdateWithProgress(configs []SSHConfig, sourceFile, targetDir string, backupGateway bool, sshTimeout int, progressChan chan<- ProgressUpdate) []UpdateResult {
	var results []UpdateResult

	for i, config := range configs {
		result := UpdateResult{
			IP: config.IP,
		}

		// 发送连接状态
		progress := ProgressUpdate{
			IP:       config.IP,
			Status:   "connecting",
			Message:  "正在连接SSH...",
			Progress: (i * 100) / len(configs),
		}
		log.Printf("发送连接状态: %s - %s", config.IP, progress.Message)
		progressChan <- progress

		// 创建SSH连接
		client, err := createSSHClient(config, sshTimeout)
		if err != nil {
			result.Success = false
			result.Message = fmt.Sprintf("SSH连接失败: %v", err)
			results = append(results, result)
			
			// 发送错误状态
			progress = ProgressUpdate{
				IP:       config.IP,
				Status:   "error",
				Message:  result.Message,
				Progress: ((i + 1) * 100) / len(configs),
			}
			log.Printf("发送错误状态: %s - %s", config.IP, progress.Message)
			progressChan <- progress
			
			// 添加短暂延迟，让前端有时间显示状态
			time.Sleep(500 * time.Millisecond)
			continue
		}

		// 发送传输状态
		progress = ProgressUpdate{
			IP:       config.IP,
			Status:   "transferring",
			Message:  "正在传输文件...",
			Progress: (i * 100) / len(configs),
		}
		log.Printf("发送传输状态: %s - %s", config.IP, progress.Message)
		progressChan <- progress

		// 传输文件
		err = scpFile(client, sourceFile, targetDir)
		
		if err != nil {
			result.Success = false
			result.Message = fmt.Sprintf("文件传输失败: %v", err)
			
			// 发送错误状态
			progress = ProgressUpdate{
				IP:       config.IP,
				Status:   "error",
				Message:  result.Message,
				Progress: ((i + 1) * 100) / len(configs),
			}
			log.Printf("发送错误状态: %s - %s", config.IP, progress.Message)
			progressChan <- progress
		} else {
			// 文件传输成功，开始处理
			progress = ProgressUpdate{
				IP:       config.IP,
				Status:   "processing",
				Message:  "正在处理文件...",
				Progress: ((i + 1) * 100) / len(configs),
			}
			log.Printf("发送处理状态: %s - %s", config.IP, progress.Message)
			progressChan <- progress
			
			// 获取文件名（不包含路径）
			filename := filepath.Base(sourceFile)
			
			// 检查是否存在 gateway 文件夹
			checkCmd := fmt.Sprintf("cd %s && [ -d gateway ] && echo 'EXISTS' || echo 'NOT_EXISTS'", targetDir)
			
			// 执行检查命令
			session, err := client.NewSession()
			if err != nil {
				result.Success = false
				result.Message = fmt.Sprintf("创建SSH会话失败: %v", err)
				
				// 发送错误状态
				progress = ProgressUpdate{
					IP:       config.IP,
					Status:   "error",
					Message:  result.Message,
					Progress: ((i + 1) * 100) / len(configs),
				}
				log.Printf("发送错误状态: %s - %s", config.IP, progress.Message)
				progressChan <- progress
			} else {
				output, err := session.CombinedOutput(checkCmd)
				session.Close()
				
				if err != nil {
					result.Success = false
					result.Message = fmt.Sprintf("检查gateway文件夹失败: %v", err)
					
					// 发送错误状态
					progress = ProgressUpdate{
						IP:       config.IP,
						Status:   "error",
						Message:  result.Message,
						Progress: ((i + 1) * 100) / len(configs),
					}
					log.Printf("发送错误状态: %s - %s", config.IP, progress.Message)
					progressChan <- progress
				} else {
					checkResult := strings.TrimSpace(string(output))
					log.Printf("检查gateway文件夹结果: %s", checkResult)
					
					if checkResult == "EXISTS" && backupGateway {
						// 存在 gateway 文件夹，用户选择备份
						progress = ProgressUpdate{
							IP:       config.IP,
							Status:   "backing",
							Message:  "正在备份gateway文件夹...",
							Progress: ((i + 1) * 100) / len(configs),
						}
						log.Printf("发送备份状态: %s - %s", config.IP, progress.Message)
						progressChan <- progress
						
						// 生成备份文件夹名称（格式：gateway_YYYYMMDD）
						now := time.Now()
						backupName := fmt.Sprintf("gateway_%s", now.Format("20060102"))
						backupCmd := fmt.Sprintf("cd %s && cp -R gateway %s", targetDir, backupName)
						
						// 执行备份命令
						err = executeSSHCommand(client, backupCmd)
						if err != nil {
							result.Success = false
							result.Message = fmt.Sprintf("备份gateway文件夹失败: %v", err)
							
							// 发送错误状态
							progress = ProgressUpdate{
								IP:       config.IP,
								Status:   "error",
								Message:  result.Message,
								Progress: ((i + 1) * 100) / len(configs),
							}
							log.Printf("发送错误状态: %s - %s", config.IP, progress.Message)
							progressChan <- progress
							continue
						}
						
						log.Printf("成功备份gateway文件夹为: %s", backupName)
					}
					
					// 无论是否备份，都执行解压（覆盖模式）
					progress = ProgressUpdate{
						IP:       config.IP,
						Status:   "extracting",
						Message:  "正在解压文件...",
						Progress: ((i + 1) * 100) / len(configs),
					}
					log.Printf("发送解压状态: %s - %s", config.IP, progress.Message)
					progressChan <- progress
					
					// 构建解压命令
					extractCmd := fmt.Sprintf("cd %s && tar -xf %s", targetDir, filename)
					
					// 执行解压命令
					err = executeSSHCommand(client, extractCmd)
					if err != nil {
						result.Success = false
						result.Message = fmt.Sprintf("文件解压失败: %v", err)
						
						// 发送错误状态
						progress = ProgressUpdate{
							IP:       config.IP,
							Status:   "error",
							Message:  result.Message,
							Progress: ((i + 1) * 100) / len(configs),
						}
						log.Printf("发送错误状态: %s - %s", config.IP, progress.Message)
						progressChan <- progress
					} else {
						// 解压成功，删除压缩包
						progress = ProgressUpdate{
							IP:       config.IP,
							Status:   "cleaning",
							Message:  "正在清理临时文件...",
							Progress: ((i + 1) * 100) / len(configs),
						}
						log.Printf("发送清理状态: %s - %s", config.IP, progress.Message)
						progressChan <- progress
						
						// 删除压缩包
						deleteCmd := fmt.Sprintf("cd %s && rm -f %s", targetDir, filename)
						err = executeSSHCommand(client, deleteCmd)
						if err != nil {
							log.Printf("删除压缩包失败: %v", err)
							// 删除失败不影响整体结果，只记录日志
						} else {
							log.Printf("成功删除压缩包: %s", filename)
						}
						
						result.Success = true
						if checkResult == "EXISTS" && backupGateway {
							now := time.Now()
							backupName := fmt.Sprintf("gateway_%s", now.Format("20060102"))
							result.Message = fmt.Sprintf("文件传输成功，已备份为%s并解压完成", backupName)
						} else {
							result.Message = "文件传输成功，解压完成"
						}
						
						// 发送成功状态
						progress = ProgressUpdate{
							IP:       config.IP,
							Status:   "success",
							Message:  result.Message,
							Progress: ((i + 1) * 100) / len(configs),
						}
						log.Printf("发送成功状态: %s - %s", config.IP, progress.Message)
						progressChan <- progress
					}
				}
			}
		}
		
		// 确保客户端连接被正确关闭
		if client != nil {
			client.Close()
		}

		results = append(results, result)
	}

	return results
}

// 批量更新文件（原有函数保持兼容）
func batchUpdate(configs []SSHConfig, sourceFile, targetDir string, sshTimeout int) []UpdateResult {
	progressChan := make(chan ProgressUpdate, 100)
	go func() {
		for range progressChan {
			// 丢弃进度更新
		}
	}()
	
	return batchUpdateWithProgress(configs, sourceFile, targetDir, true, sshTimeout, progressChan)
}

// API处理器

// 获取SSH配置列表
func getSSHConfigs(c *gin.Context) {
	configFile := c.DefaultQuery("configFile", "config.txt")
	
	configs, err := parseSSHConfig(configFile)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"configs": configs})
}

// 执行批量更新（带实时进度）
func performUpdateWithProgress(c *gin.Context) {
	log.Printf("收到批量更新请求")
	
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("请求参数解析失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}
	
	log.Printf("解析请求成功: sourceFile=%s, targetDir=%s, configsCount=%d", 
		req.SourceFile, req.TargetDir, len(req.SSHConfigs))

	// 验证源文件是否存在
	if _, err := os.Stat(req.SourceFile); os.IsNotExist(err) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "源文件不存在: " + req.SourceFile})
		return
	}

	var configs []SSHConfig
	var err error

	// 如果提供了SSH配置，使用提供的配置；否则从文件读取
	if len(req.SSHConfigs) > 0 {
		configs = req.SSHConfigs
	} else {
		configs, err = parseSSHConfig(req.ConfigFile)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}

	if len(configs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有找到有效的SSH配置"})
		return
	}

	// 设置SSE头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// 创建进度通道
	progressChan := make(chan ProgressUpdate, 100)
	
	// 启动后台任务执行批量更新
	go func() {
		defer close(progressChan)
		
		log.Printf("开始批量更新，共 %d 台设备", len(configs))
		results := batchUpdateWithProgress(configs, req.SourceFile, req.TargetDir, req.BackupGateway, globalConfig.SSH.Timeout, progressChan)
		
		// 发送最终结果
		successCount := 0
		for _, result := range results {
			if result.Success {
				successCount++
			}
		}

		summary := fmt.Sprintf("总计: %d台设备, 成功: %d台, 失败: %d台", 
			len(results), successCount, len(results)-successCount)

		log.Printf("批量更新完成: %s", summary)
		
		// 发送完成事件
		progressChan <- ProgressUpdate{
			IP:       "summary",
			Status:   "completed",
			Message:  summary,
			Progress: 100,
		}
		
		log.Printf("发送完成事件")
	}()

	// 发送进度更新
	c.Stream(func(w io.Writer) bool {
		if progress, ok := <-progressChan; ok {
			log.Printf("SSE发送进度: %s - %s - %s", progress.IP, progress.Status, progress.Message)
			c.SSEvent("progress", progress)
			// 强制刷新缓冲区
			if flusher, ok := w.(http.Flusher); ok {
				flusher.Flush()
			}
			return true
		}
		log.Printf("SSE通道关闭")
		return false
	})
}

// 执行批量更新（原有接口保持兼容）
func performUpdate(c *gin.Context) {
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	// 验证源文件是否存在
	if _, err := os.Stat(req.SourceFile); os.IsNotExist(err) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "源文件不存在: " + req.SourceFile})
		return
	}

	var configs []SSHConfig
	var err error

	// 如果提供了SSH配置，使用提供的配置；否则从文件读取
	if len(req.SSHConfigs) > 0 {
		configs = req.SSHConfigs
	} else {
		configs, err = parseSSHConfig(req.ConfigFile)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}

	if len(configs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "没有找到有效的SSH配置"})
		return
	}

	// 执行批量更新
	results := batchUpdate(configs, req.SourceFile, req.TargetDir, globalConfig.SSH.Timeout)

	// 统计结果
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	summary := fmt.Sprintf("总计: %d台设备, 成功: %d台, 失败: %d台", 
		len(results), successCount, len(results)-successCount)

	response := UpdateResponse{
		Results: results,
		Summary: summary,
	}

	c.JSON(http.StatusOK, response)
}

// 文件上传处理器
func uploadFile(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "文件上传失败: " + err.Error()})
		return
	}

	// 创建uploads目录
	uploadDir := "uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建上传目录失败: " + err.Error()})
		return
	}

	// 保存文件
	filename := filepath.Join(uploadDir, file.Filename)
	if err := c.SaveUploadedFile(file, filename); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "保存文件失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "文件上传成功",
		"filename": filename,
		"size": file.Size,
	})
}

func main() {
	// 加载配置文件
	appConfig, err := loadConfig("config.ini")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}
	
	// 设置全局配置
	globalConfig = appConfig
	
	log.Printf("配置加载成功 - 端口: %d, SSH超时: %d秒", appConfig.Server.Port, appConfig.SSH.Timeout)

	// 创建Gin路由器
	r := gin.Default()

	// 配置CORS，动态添加配置的端口
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = []string{
		"http://localhost:3000", 
		"http://localhost:9999", 
		"http://127.0.0.1:3000", 
		"http://127.0.0.1:9999",
		fmt.Sprintf("http://localhost:%d", appConfig.Server.Port),
		fmt.Sprintf("http://127.0.0.1:%d", appConfig.Server.Port),
	}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	r.Use(cors.New(corsConfig))

	// API路由
	api := r.Group("/api")
	{
		api.GET("/ssh-configs", getSSHConfigs)
		api.POST("/update", performUpdate)
		api.POST("/update-progress", performUpdateWithProgress)
		api.POST("/upload", uploadFile)
	}

	// 静态文件服务
	r.Static("/static", "./static")
	r.StaticFile("/", "./static/simple.html")

	// 启动服务器
	port := strconv.Itoa(appConfig.Server.Port)
	
	// 支持环境变量覆盖配置文件
	if envPort := os.Getenv("PORT"); envPort != "" {
		port = envPort
		log.Printf("使用环境变量PORT覆盖配置文件端口: %s", port)
	}

	fmt.Printf("服务器启动在端口 %s\n", port)
	fmt.Printf("配置文件: config.ini\n")
	fmt.Printf("API文档:\n")
	fmt.Printf("GET  /api/ssh-configs - 获取SSH配置列表\n")
	fmt.Printf("POST /api/update - 执行批量更新\n")
	fmt.Printf("POST /api/upload - 上传文件\n")
	
	log.Fatal(r.Run(":" + port))
} 