#!/bin/bash

echo "=== EMS Box 批量更新工具构建脚本 ==="

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "错误: Go未安装，请先安装Go语言环境"
    exit 1
fi

# 检查Node.js是否安装（用于前端构建）
if ! command -v node &> /dev/null; then
    echo "警告: Node.js未安装，将跳过前端构建"
    SKIP_FRONTEND=true
else
    SKIP_FRONTEND=false
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p uploads
mkdir -p static/dist

# 安装Go依赖
echo "安装Go依赖..."
go mod tidy

# 构建前端（如果Node.js可用）
if [ "$SKIP_FRONTEND" = false ]; then
    echo "构建前端..."
    cd static
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        npm install
    fi
    echo "构建前端资源..."
    npm run build
    cd ..
else
    echo "跳过前端构建"
fi

# 构建Go程序
echo "构建Go程序..."
go build -o emsbox-updater main.go

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo ""
    echo "使用方法:"
    echo "1. 编辑 config.txt 文件，配置SSH连接信息"
    echo "2. 运行程序: ./emsbox-updater"
    echo "3. 打开浏览器访问: http://localhost:8080"
else
    echo "❌ 构建失败！"
    exit 1
fi 